import { useState, useEffect } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { getAutomaticValues, type CertificateType } from '../../utils/certificateTypeMapping';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';

// Define the form schema using Zod
const gebaeudedetailsSchema = z.object({
  // Gebäudedetails Teil 1
  BedarfVerbrauch: z.enum(['V', 'B']).default('V'),
  Anlass: z.enum(['AG_VERMIETUNG', 'AG_AUSHANG', 'AG_SONST']).default('AG_VERMIETUNG'),
  Datenerhebung: z.enum(['0', '1', '2']).default('2'),
  nichtWohnGeb: z.enum(['0', '1']).default('0'),
  isGebaeudehuelle: z.enum(['0', '1']).default('1'),
  // These fields are conditionally required based on certificate type
  Nutzung1_ID: z.string().optional(),
  Nutzung1_Flaeche: z.string().optional(),
  Baujahr: z.string().min(4, 'Baujahr ist erforderlich'),
  Modernisierung: z.string().optional(),
  Wohnfläche: z.string().min(1, 'Wohnfläche ist erforderlich'),
  // These fields are conditionally required for WG/B certificate type
  Raumhöhe: z.string().optional(),
  Volumen: z.string().optional(),
  Wohneinheiten: z.string().min(1, 'Anzahl der Wohneinheiten ist erforderlich'),
  Geschosse: z.string().optional(),
  anbauSituation: z.enum(['0', '1', '2']).optional(),
  Keller_beheizt: z.enum(['0', '1']).default('0').optional(),
  Klimatisiert: z.enum(['0', '1']).default('1'),
  ergaenzendeErlaeuterungen: z.string().optional(),
  baujahrHzErz: z.string().optional(),
});

type GebaeudedetailsFormValues = z.infer<typeof gebaeudedetailsSchema>;

export const GebaeudedetailsPage1 = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [certificateType, setCertificateType] = useState<CertificateType | null>(null);
  const { activeCertificateId } = useCertificate();

  // Mark this page as visited for navigation tracking
  usePageVisit('gebaeudedetails1');
  const [initialValues, setInitialValues] = useState<Partial<GebaeudedetailsFormValues>>({
    BedarfVerbrauch: 'V' as const,
    Anlass: 'AG_VERMIETUNG' as const,
    Datenerhebung: '2' as const,
    nichtWohnGeb: '0' as const,
    isGebaeudehuelle: '1' as const,
    Nutzung1_ID: '',
    Nutzung1_Flaeche: '',
    Baujahr: '',
    Modernisierung: '',
    Wohnfläche: '',
    Raumhöhe: '',
    Volumen: '',
    Wohneinheiten: '',
    Geschosse: '',
    anbauSituation: undefined,
    Keller_beheizt: '0' as const,
    Klimatisiert: '1' as const,
    ergaenzendeErlaeuterungen: '',
    baujahrHzErz: '',
  });
  const [isLoading, setIsLoading] = useState(true);

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Fetch existing data
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'gebaeudedetails1', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('gebaeudedetails1')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      const certType = certificateData.certificate_type as CertificateType;
      setCertificateType(certType);

      // Update initial values with automatic values based on certificate type
      const automaticValues = getAutomaticValues(certType);
      setInitialValues(prev => ({
        ...prev,
        ...automaticValues
      }));
    }
  }, [certificateData]);

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData?.gebaeudedetails1) {
      // Merge existing data with automatic values based on current certificate type
      const automaticValues = certificateType ? getAutomaticValues(certificateType) : {};
      const savedData = existingData.gebaeudedetails1 as Partial<GebaeudedetailsFormValues>;
      setInitialValues(prev => ({
        ...prev,
        ...savedData,
        ...automaticValues // Override with automatic values
      }));
    }
    setIsLoading(false);
  }, [existingData, certificateType]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: GebaeudedetailsFormValues) => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // Create a copy of the data to modify
      const dataToSave = { ...data };

      // Remove Nutzung1 fields if certificate type is not NWG/V
      if (certificateType !== 'NWG/V') {
        delete dataToSave.Nutzung1_ID;
        delete dataToSave.Nutzung1_Flaeche;
      }

      // Remove WG/B specific fields if certificate type is not WG/B
      if (certificateType !== 'WG/B') {
        delete dataToSave.Raumhöhe;
        delete dataToSave.Volumen;
        delete dataToSave.Geschosse;
        delete dataToSave.anbauSituation;
      }

      // Remove Keller_beheizt field if certificate type is not WG/V
      if (certificateType !== 'WG/V') {
        delete dataToSave.Keller_beheizt;
      }

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          gebaeudedetails1: dataToSave,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'gebaeudedetails1', activeCertificateId] });
      // Navigate to the next page
      navigate({ to: '/erfassen/gebaeudedetails2' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);

      // Automatically set BedarfVerbrauch and nichtWohnGeb based on certificate type
      const automaticValues = certificateType ? getAutomaticValues(certificateType) : {};
      const valueWithAutomaticFields = {
        ...value,
        ...automaticValues
      };

      // Additional validation for NWG/V certificate type
      if (certificateType === 'NWG/V') {
        if (!valueWithAutomaticFields.Nutzung1_ID) {
          setSubmitError('Nutzung1 ID ist für Nichtwohngebäude erforderlich');
          return;
        }
        if (!valueWithAutomaticFields.Nutzung1_Flaeche) {
          setSubmitError('Nutzung1 Fläche ist für Nichtwohngebäude erforderlich');
          return;
        }
      }

      // Additional validation for WG/B certificate type
      if (certificateType === 'WG/B') {
        if (!valueWithAutomaticFields.Raumhöhe) {
          setSubmitError('Raumhöhe ist für Bedarfsausweise erforderlich');
          return;
        }
        if (!valueWithAutomaticFields.Volumen) {
          setSubmitError('Gebäudevolumen ist für Bedarfsausweise erforderlich');
          return;
        }
        if (!valueWithAutomaticFields.Geschosse) {
          setSubmitError('Anzahl der Geschosse ist für Bedarfsausweise erforderlich');
          return;
        }
        if (!valueWithAutomaticFields.anbauSituation) {
          setSubmitError('Anbausituation ist für Bedarfsausweise erforderlich');
          return;
        }
      }

      saveMutation.mutate(valueWithAutomaticFields as GebaeudedetailsFormValues);
    },
  });

  // Update form values when initialValues change
  useEffect(() => {
    form.reset(initialValues);
  }, [initialValues]); // Removed 'form' from dependency array to prevent infinite re-renders

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = true
  }: {
    name: keyof GebaeudedetailsFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={typeof state.value === 'boolean' ? String(state.value) : (state.value ?? '')}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = true
  }: {
    name: keyof GebaeudedetailsFormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={typeof state.value === 'boolean' ? String(state.value) : (state.value ?? '')}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for checkbox fields
  const CheckboxField = ({
    name,
    label,
  }: {
    name: keyof GebaeudedetailsFormValues;
    label: string;
  }) => {
    const { state, handleChange } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4 flex items-center">
        <input
          id={name}
          name={name}
          type="checkbox"
          checked={state.value === '1'}
          onChange={(e) => handleChange(e.target.checked ? '1' : '0')}
          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
        />
        <label htmlFor={name} className="ml-2 block text-sm text-gray-700">
          {label}
        </label>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Gebäudedetails erfassen (Teil 1)
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die grundlegenden Gebäudedaten ein.
      </p>


      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Allgemeine Gebäudeinformationen
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectField
              name="Anlass"
              label="Anlass"
              options={[
                { value: 'AG_VERMIETUNG', label: 'Vermietung/Verkauf' },
                { value: 'AG_AUSHANG', label: 'Aushang' },
                { value: 'AG_SONST', label: 'Sonstiges (freiwillig)' },
              ]}
            />

            <SelectField
              name="Datenerhebung"
              label="Datenerhebung durch"
              options={[
                { value: '0', label: 'Eigentümer' },
                { value: '1', label: 'Aussteller' },
                { value: '2', label: 'Beide' },
              ]}
            />

            <SelectField
              name="isGebaeudehuelle"
              label="Gebäudehülle"
              options={[
                { value: '1', label: 'Ein Gebäude' },
                { value: '0', label: 'Mehrere Gebäude' },
              ]}
            />
          </div>
        </div>

        {certificateType === 'NWG/V' && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Nutzungsdaten
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                name="Nutzung1_ID"
                label="Nutzung1 ID (für Nichtwohngebäude)"
                placeholder="z.B. 91"
                required={certificateType === 'NWG/V'}
              />

              <FormField
                name="Nutzung1_Flaeche"
                label="Nutzung1 Fläche in m² (für Nichtwohngebäude)"
                placeholder="z.B. 1000"
                required={certificateType === 'NWG/V'}
              />
            </div>
          </div>
        )}

        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Gebäudedaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              name="Baujahr"
              label="Baujahr"
              placeholder="z.B. 1975"
              required={true}
            />

            <FormField
              name="Modernisierung"
              label="Jahr der energetischen Sanierung"
              placeholder="z.B. 2000"
              required={false}
            />

            <FormField
              name="Wohnfläche"
              label="Wohnfläche in m²"
              placeholder="z.B. 430"
              required={true}
            />

            {certificateType === 'WG/B' && (
              <FormField
                name="Raumhöhe"
                label="Durchschnittliche Raumhöhe in m"
                placeholder="z.B. 2.5"
                required={certificateType === 'WG/B'}
              />
            )}

            {certificateType === 'WG/B' && (
              <FormField
                name="Volumen"
                label="Gebäudevolumen in m³"
                placeholder="Wird ggf. automatisch berechnet"
                required={certificateType === 'WG/B'}
              />
            )}

            <FormField
              name="Wohneinheiten"
              label="Anzahl Wohneinheiten"
              placeholder="z.B. 3"
              required={true}
            />

            {certificateType === 'WG/B' && (
              <FormField
                name="Geschosse"
                label="Anzahl der Geschosse"
                placeholder="z.B. 3"
                required={certificateType === 'WG/B'}
              />
            )}

            {certificateType === 'WG/B' && (
              <SelectField
                name="anbauSituation"
                label="Anbausituation"
                options={[
                  { value: '0', label: 'Freistehend' },
                  { value: '1', label: 'Einseitig angebaut' },
                  { value: '2', label: 'Zweiseitig angebaut' },
                ]}
                required={certificateType === 'WG/B'}
              />
            )}

            {certificateType === 'WG/V' && (
              <CheckboxField
                name="Keller_beheizt"
                label="Keller beheizt"
              />
            )}

            <CheckboxField
              name="Klimatisiert"
              label="Kühlanlage vorhanden"
            />

            <div className="md:col-span-2">
              <FormField
                name="ergaenzendeErlaeuterungen"
                label="Ergänzende Erläuterungen"
                placeholder="Zusätzliche Informationen zum Gebäude"
                required={false}
              />
            </div>

            <FormField
              name="baujahrHzErz"
              label="Baujahr Wärmeerzeuger"
              placeholder="z.B. 1980"
              required={false}
            />
          </div>
        </div>

        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

        <div className="flex justify-between mt-8">
          <Link
            to="/erfassen/objektdaten"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={form.state.isSubmitting || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
          </button>
        </div>
      </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};