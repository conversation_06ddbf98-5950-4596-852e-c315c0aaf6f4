import {
  Outlet,
  createRoute,
  createRootRouteWithContext,
  createRouter,
  redirect
} from '@tanstack/react-router';
import { MainLayout } from '../layouts/MainLayout';
import { HomePage } from '../pages/HomePage';
import { ZusammenfassungPage } from '../pages/erfassen/ZusammenfassungPage';
import { LoginPage } from '../pages/LoginPage';
import { RegisterPage } from '../pages/RegisterPage';
import { ForgotPasswordPage } from '../pages/ForgotPasswordPage';
import { ResetPasswordPage } from '../pages/ResetPasswordPage';
import { AdminPage } from '../pages/AdminPage';
import { KontoPage } from '../pages/KontoPage';
import { MeineZertifikatePage } from '../pages/MeineZertifikatePage';
import { PaymentSuccessPage } from '../pages/PaymentSuccessPage';
import { PaymentCancelPage } from '../pages/PaymentCancelPage';
import { NotFoundPage } from '../pages/NotFoundPage';
import { AgbPage } from '../pages/legal/AgbPage';
import { DatenschutzPage } from '../pages/legal/DatenschutzPage';
import { WiderrufPage } from '../pages/legal/WiderrufPage';

import { ObjektdatenPage } from '../pages/erfassen/ObjektdatenPage';
import { GebaeudedetailsPage1 } from '../pages/erfassen/GebaeudedetailsPage1';
import { GebaeudedetailsPage2 } from '../pages/erfassen/GebaeudedetailsPage2';
import { FensterPage } from '../pages/erfassen/FensterPage';
import { HeizungPage } from '../pages/erfassen/HeizungPage';
import { TwwLueftungPage } from '../pages/erfassen/TwwLueftungPage';
import { VerbrauchPage } from '../pages/erfassen/VerbrauchPage';
import { CertificateTypePage } from '../pages/erfassen/CertificateTypePage';
import {
  checkAuth,
  checkCertificateType,
  getUserRole // Import getUserRole
} from '../utils/routeLoaders';
import type { RouterContext } from '../utils/routeLoaders';

// Define the root route
const rootRoute = createRootRouteWithContext<RouterContext>()({
  component: () => (
    <MainLayout>
      <Outlet />
    </MainLayout>
  ),
});

// Define the routes
const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: HomePage,
});



const certificateTypeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/certificate-type',
  component: CertificateTypePage,
  loader: async () => {
    await checkAuth('/erfassen/certificate-type');
  },
});

const objektdatenRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/objektdaten',
  component: ObjektdatenPage,
  loader: async () => {
    await checkAuth('/erfassen/objektdaten');
  },
});

const gebaeudedetails1Route = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/gebaeudedetails1',
  component: GebaeudedetailsPage1,
  loader: async () => {
    await checkAuth('/erfassen/gebaeudedetails1');
  },
});

const gebaeudedetails2Route = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/gebaeudedetails2',
  component: GebaeudedetailsPage2,
  loader: async () => {
    await checkAuth('/erfassen/gebaeudedetails2');
  },
});

const fensterRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/fenster',
  component: FensterPage,
  loader: async () => {
    await checkAuth('/erfassen/fenster');
    await checkCertificateType({
      requiredType: 'WG/B',
      redirectTo: '/erfassen/objektdaten'
    });
  },
});

const heizungRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/heizung',
  component: HeizungPage,
  loader: async () => {
    await checkAuth('/erfassen/heizung');
    await checkCertificateType({
      requiredType: 'WG/B',
      redirectTo: '/erfassen/verbrauch' // Redirect to verbrauch for non-WG/B types
    });
  },
});

const twwLueftungRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/tww-lueftung',
  component: TwwLueftungPage,
  loader: async () => {
    await checkAuth('/erfassen/tww-lueftung');
    await checkCertificateType({
      requiredType: 'WG/B',
      redirectTo: '/erfassen/verbrauch' // Redirect to verbrauch for non-WG/B types
    });
  },
});

const verbrauchRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/verbrauch',
  component: VerbrauchPage,
  loader: async () => {
    await checkAuth('/erfassen/verbrauch');
  },
});

const zusammenfassungRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/erfassen/zusammenfassung',
  component: ZusammenfassungPage,
  loader: async () => {
    await checkAuth('/erfassen/zusammenfassung');
  },
});

const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: LoginPage,
});

const registerRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/register',
  component: RegisterPage,
});

const forgotPasswordRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/forgot-password',
  component: ForgotPasswordPage,
});

const resetPasswordRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/reset-password',
  component: ResetPasswordPage,
  beforeLoad: ({ context }) => {
    // Don't redirect if still loading auth state
    if (context.loading) {
      return;
    }

    // This route should only be accessible with a valid session from the reset link
    if (!context.session) {
      throw redirect({
        to: '/forgot-password',
      });
    }
  },
});

const adminRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/admin',
  component: AdminPage,
  loader: async () => {
    await checkAuth('/admin');
    const userRole = await getUserRole();
    if (userRole !== 'admin') {
      throw redirect({ to: '/' }); // Redirect non-admins to home
    }
  },
});

const kontoRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/konto',
  component: KontoPage,
  loader: async () => {
    await checkAuth('/konto');
  },
});

const meineZertifikateRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/meine-zertifikate',
  component: MeineZertifikatePage,
  loader: async () => {
    await checkAuth('/meine-zertifikate');
  },
});

const paymentSuccessRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/payment-success',
  component: PaymentSuccessPage,
  loader: async () => {
    await checkAuth('/payment-success');
  },
});

const paymentCancelRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/payment-cancel',
  component: PaymentCancelPage,
  loader: async () => {
    await checkAuth('/payment-cancel');
  },
});

const agbRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/legal/agb',
  component: AgbPage,
});

const datenschutzRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/legal/datenschutz',
  component: DatenschutzPage,
});

const widerrufRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/legal/widerruf',
  component: WiderrufPage,
});

const notFoundRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '*',
  component: NotFoundPage,
});

// Create the route tree
const routeTree = rootRoute.addChildren([
  indexRoute,
  certificateTypeRoute,
  objektdatenRoute,
  gebaeudedetails1Route,
  gebaeudedetails2Route,
  fensterRoute,
  heizungRoute,
  twwLueftungRoute,
  verbrauchRoute,
  zusammenfassungRoute,
  loginRoute,
  registerRoute,
  forgotPasswordRoute,
  resetPasswordRoute,
  adminRoute,
  kontoRoute,
  meineZertifikateRoute,
  paymentSuccessRoute,
  paymentCancelRoute,
  agbRoute,
  datenschutzRoute,
  widerrufRoute,
  notFoundRoute,
]);

// Create the router
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  context: {
    user: null,
    session: null,
    loading: true, // Start with loading true until auth state is determined
  },
});

// Register the router for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}
